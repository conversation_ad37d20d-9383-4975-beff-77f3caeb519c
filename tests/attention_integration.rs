//! Integration tests for the attention system.
//!
//! This module contains comprehensive integration tests that verify
//! the entire attention system works correctly as a whole.

use qilin_inference::attention::*;
use qilin_inference::tensor::{Tensor, Shape};
use qilin_inference::tensor::cpu::{<PERSON><PERSON>T<PERSON>or, CpuTensorFactory};
use qilin_inference::tensor::TensorFactory;

/// Helper function to create test tensors.
fn create_test_tensor(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
    let shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
    CpuTensorFactory::randn(&shape, 0.0, 0.1).unwrap()
}

/// Test the complete attention pipeline from basic to advanced features.
#[test]
fn test_attention_system_integration() {
    // Test configuration
    let hidden_size = 128;
    let num_heads = 8;
    let head_dim = hidden_size / num_heads;
    let batch_size = 2;
    let seq_len = 10;
    
    // Create configuration
    let config = AttentionConfig::new(hidden_size, num_heads)
        .with_dropout(0.1)
        .with_max_seq_len(1024);
    
    println!("Testing attention system with config: {}", config.summary());
    
    // Test 1: Basic scaled dot-product attention
    println!("1. Testing ScaledDotProductAttention...");
    let sdp_attention = ScaledDotProductAttention::<f32>::new(head_dim, 0.0, false).unwrap();
    
    let query = create_test_tensor(batch_size, seq_len, head_dim);
    let key = create_test_tensor(batch_size, seq_len, head_dim);
    let value = create_test_tensor(batch_size, seq_len, head_dim);
    
    let (output, weights) = sdp_attention.compute_attention(&query, &key, &value, None).unwrap();
    assert_eq!(output.shape().dims(), &[batch_size, seq_len, head_dim]);
    assert_eq!(weights.shape().dims(), &[batch_size, seq_len, seq_len]);
    println!("   ✓ ScaledDotProductAttention works correctly");
    
    // Test 2: Multi-head attention
    println!("2. Testing MultiHeadAttention...");
    let mha = MultiHeadAttention::<f32>::new(config.clone()).unwrap();
    
    let input = create_test_tensor(batch_size, seq_len, hidden_size);
    let (mha_output, mha_weights) = mha.compute_attention(&input, &input, &input, None).unwrap();
    assert_eq!(mha_output.shape().dims(), &[batch_size, seq_len, hidden_size]);
    assert_eq!(mha_weights.shape().dims(), &[batch_size, num_heads, seq_len, seq_len]);
    println!("   ✓ MultiHeadAttention works correctly");
    
    // Test 3: Self-attention variant
    println!("3. Testing SelfAttention variant...");
    let self_attn = SelfAttention::<f32>::new(config.clone()).unwrap();
    let self_output = self_attn.forward(&input, None, None).unwrap();
    assert_eq!(self_output.shape().dims(), &[batch_size, seq_len, hidden_size]);
    println!("   ✓ SelfAttention variant works correctly");
    
    // Test 4: Cross-attention variant
    println!("4. Testing CrossAttention variant...");
    let cross_attn = CrossAttention::<f32>::new(config.clone()).unwrap();
    let encoder_output = create_test_tensor(batch_size, seq_len + 5, hidden_size);
    let cross_output = cross_attn.forward(&input, Some(&encoder_output), None).unwrap();
    assert_eq!(cross_output.shape().dims(), &[batch_size, seq_len, hidden_size]);
    println!("   ✓ CrossAttention variant works correctly");
    
    // Test 5: Causal attention variant
    println!("5. Testing CausalAttention variant...");
    let causal_config = AttentionConfig::causal(hidden_size, num_heads);
    let causal_attn = CausalAttention::<f32>::new(causal_config).unwrap();
    let causal_output = causal_attn.forward(&input, None, None).unwrap();
    assert_eq!(causal_output.shape().dims(), &[batch_size, seq_len, hidden_size]);
    println!("   ✓ CausalAttention variant works correctly");
    
    println!("✅ All attention components work correctly!");
}

/// Test KV caching system integration.
#[test]
fn test_kv_cache_integration() {
    println!("Testing KV cache system integration...");
    
    let hidden_size = 64;
    let num_heads = 4;
    let head_dim = hidden_size / num_heads;
    
    // Create cache configuration
    let cache_config = CacheConfig::new(1024, 8, num_heads, head_dim);
    let mut cache = KVCache::<f32>::new(cache_config).unwrap();
    
    // Test cache operations
    let seq_len1 = 5;
    let seq_len2 = 3;
    
    let keys1 = create_test_tensor(seq_len1, num_heads, head_dim);
    let values1 = create_test_tensor(seq_len1, num_heads, head_dim);
    
    // Store initial sequence
    cache.store("seq_1", keys1, values1).unwrap();
    assert!(cache.contains("seq_1"));
    assert_eq!(cache.get_sequence_length("seq_1"), Some(seq_len1));
    
    // Append to sequence
    let keys2 = create_test_tensor(seq_len2, num_heads, head_dim);
    let values2 = create_test_tensor(seq_len2, num_heads, head_dim);
    cache.append("seq_1", keys2, values2).unwrap();
    assert_eq!(cache.get_sequence_length("seq_1"), Some(seq_len1 + seq_len2));
    
    // Retrieve cached data
    let (cached_keys, cached_values) = cache.get("seq_1").unwrap();
    assert_eq!(cached_keys.shape().dims(), &[seq_len1 + seq_len2, num_heads, head_dim]);
    assert_eq!(cached_values.shape().dims(), &[seq_len1 + seq_len2, num_heads, head_dim]);
    
    println!("   ✓ KV cache operations work correctly");
    
    // Test cache statistics
    let stats = cache.stats();
    assert_eq!(stats.num_sequences, 1);
    assert!(stats.memory_usage > 0);
    
    println!("   ✓ Cache statistics work correctly");
    println!("✅ KV cache system works correctly!");
}

/// Test incremental attention with caching.
#[test]
fn test_incremental_attention_integration() {
    println!("Testing incremental attention integration...");
    
    let hidden_size = 64;
    let num_heads = 4;
    let batch_size = 1;
    
    let config = AttentionConfig::new(hidden_size, num_heads)
        .with_max_seq_len(1024);
    
    let mut inc_attn = IncrementalAttention::<f32>::new(config).unwrap();
    
    // Initial forward pass
    let initial_seq_len = 3;
    let initial_input = create_test_tensor(batch_size, initial_seq_len, hidden_size);
    
    let (initial_output, initial_weights) = inc_attn
        .forward_initial("test_seq", &initial_input)
        .unwrap();
    
    assert_eq!(initial_output.shape().dims(), &[batch_size, initial_seq_len, hidden_size]);
    assert_eq!(initial_weights.shape().dims(), &[batch_size, num_heads, initial_seq_len, initial_seq_len]);
    assert!(inc_attn.is_cached("test_seq"));
    assert_eq!(inc_attn.get_sequence_length("test_seq"), Some(initial_seq_len));
    
    println!("   ✓ Initial forward pass works correctly");
    
    // Incremental forward pass
    let new_seq_len = 2;
    let new_input = create_test_tensor(batch_size, new_seq_len, hidden_size);
    
    let (inc_output, inc_weights) = inc_attn
        .forward_incremental("test_seq", &new_input)
        .unwrap();
    
    assert_eq!(inc_output.shape().dims(), &[batch_size, new_seq_len, hidden_size]);
    assert_eq!(inc_weights.shape().dims(), &[batch_size, num_heads, new_seq_len, initial_seq_len + new_seq_len]);
    assert_eq!(inc_attn.get_sequence_length("test_seq"), Some(initial_seq_len + new_seq_len));
    
    println!("   ✓ Incremental forward pass works correctly");
    
    // Test multiple sequences
    let seq2_input = create_test_tensor(batch_size, 4, hidden_size);
    let _ = inc_attn.forward_initial("test_seq_2", &seq2_input).unwrap();
    
    assert!(inc_attn.is_cached("test_seq"));
    assert!(inc_attn.is_cached("test_seq_2"));
    assert_eq!(inc_attn.get_sequence_length("test_seq"), Some(5));
    assert_eq!(inc_attn.get_sequence_length("test_seq_2"), Some(4));
    
    println!("   ✓ Multiple sequence handling works correctly");
    
    // Test cache management
    inc_attn.clear_sequence("test_seq").unwrap();
    assert!(!inc_attn.is_cached("test_seq"));
    assert!(inc_attn.is_cached("test_seq_2"));
    
    inc_attn.clear_all();
    assert!(!inc_attn.is_cached("test_seq_2"));
    
    println!("   ✓ Cache management works correctly");
    println!("✅ Incremental attention system works correctly!");
}

/// Test error handling and edge cases.
#[test]
fn test_attention_error_handling() {
    println!("Testing attention error handling...");
    
    let config = AttentionConfig::new(64, 4);
    
    // Test invalid configurations
    let invalid_config = AttentionConfig::new(65, 4); // Not divisible
    assert!(invalid_config.validate().is_err());
    println!("   ✓ Invalid configuration detection works");
    
    // Test dimension mismatches
    let mha = MultiHeadAttention::<f32>::new(config.clone()).unwrap();
    let input1 = create_test_tensor(1, 5, 64);
    let input2 = create_test_tensor(2, 5, 64); // Different batch size
    
    let result = mha.compute_attention(&input1, &input2, &input1, None);
    assert!(result.is_err());
    println!("   ✓ Dimension mismatch detection works");
    
    // Test cross-attention without context
    let cross_attn = CrossAttention::<f32>::new(config).unwrap();
    let result = cross_attn.forward(&input1, None, None);
    assert!(result.is_err());
    println!("   ✓ Missing context detection works");
    
    // Test incremental attention without initial
    let mut inc_attn = IncrementalAttention::<f32>::new(
        AttentionConfig::new(64, 4).with_max_seq_len(1024)
    ).unwrap();
    let result = inc_attn.forward_incremental("missing_seq", &input1);
    assert!(result.is_err());
    println!("   ✓ Missing cache entry detection works");
    
    println!("✅ Error handling works correctly!");
}

/// Test performance characteristics and memory usage.
#[test]
fn test_attention_performance_characteristics() {
    println!("Testing attention performance characteristics...");
    
    let config = AttentionConfig::new(512, 8);
    let mha = MultiHeadAttention::<f32>::new(config).unwrap();
    
    // Test with different sequence lengths
    let batch_size = 1;
    let hidden_size = 512;
    
    for seq_len in [10, 50, 100, 200] {
        let input = create_test_tensor(batch_size, seq_len, hidden_size);
        
        let start = std::time::Instant::now();
        let (output, weights) = mha.compute_attention(&input, &input, &input, None).unwrap();
        let duration = start.elapsed();
        
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
        assert_eq!(weights.shape().dims(), &[batch_size, 8, seq_len, seq_len]);
        
        println!("   ✓ Seq len {}: {:?}", seq_len, duration);
    }
    
    println!("✅ Performance characteristics verified!");
}
